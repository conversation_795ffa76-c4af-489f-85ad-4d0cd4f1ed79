"use client"

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Divider,
  Badge,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Cloud as CloudIcon,
  Storage as StorageIcon,
  AccessTime as AccessTimeIcon,
  ChevronRight as ChevronRightIcon,
  MoreHoriz as MoreHorizIcon,
  Error as ErrorIcon,
  Dataset as DatasetIcon,
  CloudDone as CloudDoneIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  Schedule as ScheduleIcon,
  HourglassTop as HourglassTopIcon
} from '@mui/icons-material';
import Link from 'next/link';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useRecentDatasets } from '@/lib/hooks/useDatasets';
import { useRecentJobs } from '@/lib/hooks/useJobs';
import { formatDashboardDate, getStatusDisplay } from '@/utils/jobUtils';


// Dashboard data interfaces
interface DashboardJob {
  id: string;
  name: string;
  status: string;
  created_at: string;
}

interface DashboardDataset {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// Status indicator component
interface StatusIndicatorProps {
  status?: 'online' | 'warning' | 'offline' | 'normal';
  label: string;
  value: string | number;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status = 'online', label, value }) => {
  const statusColors = {
    online: 'success',
    warning: 'warning',
    offline: 'error',
    normal: 'success'
  } as const;
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Badge
        variant="dot"
        sx={{ mr: 1 }}
        color={statusColors[status]}
      />
      <Box component="span" sx={{ color: 'text.secondary', typography: 'body2' }}>
        {label}:
      </Box>
      <Box component="span" sx={{ ml: 0.5, typography: 'body2', fontWeight: 'bold' }}>
        {value}
      </Box>
    </Box>
  );
};

// Custom card component
interface DashboardCardProps {
  title: string;
  children: React.ReactNode;
  icon: React.ElementType;
  action?: React.ReactNode;
  height?: string | number;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, children, icon, action, height = 'auto' }) => {
  const IconComponent = icon;

  return (
    <Card elevation={0} sx={{
      height,
      display: 'flex',
      flexDirection: 'column',
      minHeight: '400px' // Ensure minimum height for symmetry
    }}>
      <CardContent sx={{ p: 2, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && <IconComponent color="primary" sx={{ mr: 1 }} />}
            <Box component="h2" sx={{ typography: 'h6', m: 0 }}>{title}</Box>
          </Box>
          {action}
        </Box>
        <Divider sx={{ my: 1 }} />
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {children}
        </Box>
      </CardContent>
    </Card>
  );
};

export default function Home() {
    const { data: recentJobsData, error: jobsError, isLoading: jobsLoading } = useRecentJobs(5);
    const { data: recentDatasetsData, error: datasetsError, isLoading: datasetsLoading } = useRecentDatasets(5);
    const [error, setError] = useState<string | null>(null);

    // Get real data from hooks with proper typing
    const recentJobs: DashboardJob[] = recentJobsData?.data || [];
    const recentDatasets: DashboardDataset[] = recentDatasetsData?.data || [];



    // Status icon mapping for job status indicators
    const getJobStatusIcon = (status: string): React.ReactNode => {
        const statusDisplay = getStatusDisplay(status);
        switch(statusDisplay.text.toLowerCase()) {
            case 'completed': return <CheckCircleIcon sx={{ color: 'success.main', fontSize: 24 }} />;
            case 'in progress':
            case 'processing': return <SettingsIcon sx={{ color: 'info.main', fontSize: 24 }} />;
            case 'partially completed': return <HourglassTopIcon sx={{ color: 'warning.main', fontSize: 24 }} />;
            case 'failed': return <ErrorIcon sx={{ color: 'error.main', fontSize: 24 }} />;
            case 'queued': return <ScheduleIcon sx={{ color: 'warning.main', fontSize: 24 }} />;
            default: return <ScheduleIcon sx={{ color: 'grey.500', fontSize: 24 }} />;
        }
    };

    useEffect(() => {
        if (datasetsError) {
            setError(datasetsError.message || 'Failed to load recent datasets');
        }
        if (jobsError) {
            setError(jobsError.message || 'Failed to load recent jobs');
        }
    }, [datasetsError, jobsError]);

    return (
        <PageContainer>
            <Box sx={{ px: 2, pt: 2 }}>
                <Box component="h1" sx={{ typography: 'h4', mb: 3, fontWeight: 'bold' }}>
                    Dashboard
                </Box>

                <Grid container spacing={3}>
                    {/* Cloud Status Card */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Cloud Status"
                            icon={CloudIcon}
                            height="100%"
                        >
                            <StatusIndicator status="online" label="Positioning Cloud" value="Connected" />
                            <StatusIndicator status="online" label="Worker Nodes" value="24 Connected" />
                            <StatusIndicator status="normal" label="System Load" value="3%" />

                            <Box sx={{ mt: 2, mb: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Storage Usage</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>3 TB / 100 TB</Box>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={3}
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                                />
                            </Box>

                            <Box sx={{ mt: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Box component="span" sx={{ typography: 'body2' }}>Current Job Capacity</Box>
                                    <Box component="span" sx={{ typography: 'body2', fontWeight: 'medium' }}>0 / 2000 Jobs</Box>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={0.06}
                                    sx={{ mt: 1, height: 8, borderRadius: 4 }}
                                />
                            </Box>
                        </DashboardCard>
                    </Grid>

                    {/* Recent Jobs */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Recent Jobs"
                            icon={CloudDoneIcon}
                            action={
                                <Button
                                    component={Link}
                                    href="/jobs"
                                    size="small"
                                    variant="text"
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            {jobsLoading ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                                    <CircularProgress size={24} />
                                </Box>
                            ) : recentJobs.length === 0 ? (
                                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                                    No recent jobs found
                                </Box>
                            ) : (
                                <List sx={{ px: 0, flex: 1 }}>
                                    {recentJobs.map((job, index) => (
                                        <React.Fragment key={job.id}>
                                            <ListItem
                                                component={Link}
                                                href={`/jobs/${job.id}`}
                                                sx={{
                                                    py: 1,
                                                    px: 2,
                                                    minHeight: 72, // Fixed height to match datasets
                                                    alignItems: 'center',
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    }
                                                }}
                                            >
                                                <Box sx={{
                                                    mr: 2,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    width: 32, // Match avatar width
                                                    height: 32, // Match avatar height
                                                    justifyContent: 'center'
                                                }}>
                                                    {getJobStatusIcon(job.status)}
                                                </Box>
                                                <ListItemText
                                                    primaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    secondaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    primary={
                                                        <Box component="span" sx={{
                                                            typography: 'body2',
                                                            fontWeight: 'medium',
                                                            maxWidth: 200,
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis',
                                                            whiteSpace: 'nowrap',
                                                            lineHeight: 1.5 // Consistent line height
                                                        }}>
                                                            {job.name}
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            mt: 0.5,
                                                            lineHeight: 1.2 // Consistent line height
                                                        }}>
                                                            <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                                <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                                {formatDashboardDate(job.created_at)}
                                                            </Box>
                                                        </Box>
                                                    }
                                                />
                                                <ChevronRightIcon color="action" />
                                            </ListItem>
                                            {index < recentJobs.length - 1 && <Divider variant="inset" component="li" />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </DashboardCard>
                    </Grid>

                    {/* Recent Datasets */}
                    <Grid item xs={12} lg={4}>
                        <DashboardCard
                            title="Recent Datasets"
                            icon={DatasetIcon}
                            action={
                                <Button
                                    component={Link}
                                    href="/datasets"
                                    size="small"
                                    variant="text"
                                    endIcon={<MoreHorizIcon />}
                                >
                                    View All
                                </Button>
                            }
                            height="100%"
                        >
                            {datasetsLoading ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                                    <CircularProgress size={24} />
                                </Box>
                            ) : recentDatasets.length === 0 ? (
                                <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
                                    No recent datasets found
                                </Box>
                            ) : (
                                <List sx={{ px: 0, flex: 1 }}>
                                    {recentDatasets.map((dataset, index) => (
                                        <React.Fragment key={dataset.id}>
                                            <ListItem
                                                component={Link}
                                                href={`/datasets/${dataset.id}`}
                                                sx={{
                                                    py: 1,
                                                    px: 2,
                                                    minHeight: 72, // Fixed height to match jobs
                                                    alignItems: 'center',
                                                    '&:hover': {
                                                        bgcolor: 'action.hover',
                                                    }
                                                }}
                                            >
                                                <ListItemAvatar sx={{ minWidth: 48 }}> {/* Consistent spacing */}
                                                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.light' }}>
                                                        <StorageIcon />
                                                    </Avatar>
                                                </ListItemAvatar>
                                                <ListItemText
                                                    primaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    secondaryTypographyProps={{
                                                        component: 'div'
                                                    }}
                                                    primary={
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            overflow: 'hidden',
                                                            lineHeight: 1.5 // Consistent line height
                                                        }}>
                                                            <Box component="span" sx={{
                                                                typography: 'body2',
                                                                fontWeight: 'medium',
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap',
                                                                flexShrink: 0,
                                                                maxWidth: dataset.description ? '60%' : '100%'
                                                            }}>
                                                                {dataset.name}
                                                            </Box>
                                                            {dataset.description && (
                                                                <>
                                                                    <Box component="span" sx={{ mx: 1, color: 'text.secondary' }}>
                                                                        —
                                                                    </Box>
                                                                    <Box component="span" sx={{
                                                                        typography: 'body2',
                                                                        color: 'text.secondary',
                                                                        overflow: 'hidden',
                                                                        textOverflow: 'ellipsis',
                                                                        whiteSpace: 'nowrap',
                                                                        flex: 1,
                                                                        minWidth: 0
                                                                    }}>
                                                                        {dataset.description}
                                                                    </Box>
                                                                </>
                                                            )}
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box sx={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            mt: 0.5,
                                                            lineHeight: 1.2 // Consistent line height
                                                        }}>
                                                            <Box component="span" sx={{ typography: 'caption', color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
                                                                <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                                                {formatDashboardDate(dataset.created_at)}
                                                            </Box>
                                                        </Box>
                                                    }
                                                />
                                                <ChevronRightIcon color="action" />
                                            </ListItem>
                                            {index < recentDatasets.length - 1 && <Divider variant="inset" component="li" />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            )}
                        </DashboardCard>
                    </Grid>
                </Grid>
            </Box>
            
            {error && <ErrorDialog open={!!error} onClose={() => setError(null)} error={error} />}
        </PageContainer>
    );
}
