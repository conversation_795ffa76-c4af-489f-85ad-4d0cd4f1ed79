import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { name, description, categoryId, variableOverrides, filePaths, fileTypes } = await request.json();

    const { data, error } = await supabase.rpc('create_dataset_with_files', {
        p_user_id: userId,
        p_name: name,
        p_description: description,
        p_category_id: categoryId || null,
        p_variable_overrides: variableOverrides || null,
        p_file_paths: filePaths,
        p_file_types: fileTypes
    });

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const GET = withAuth(async (userId) => {
    const supabase = createClient();
    const { data, error } = await supabase
        .from('datasets')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const DELETE = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    const { error } = await supabase
        .from('datasets')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
});
